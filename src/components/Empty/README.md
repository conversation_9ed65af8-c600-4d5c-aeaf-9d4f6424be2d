# Empty 组件

自定义的空状态组件，用于替换 acud 默认的 Empty 组件。

## 功能特性

- 🎨 统一的空状态设计风格
- 🖼️ 支持自定义图标和描述
- 🔧 支持自定义操作按钮
- 📱 响应式设计，适配不同容器
- 🎯 通过 ConfigProvider 全局配置，自动应用到所有 acud 组件

## 使用方式

### 1. 全局配置（推荐）

通过 ConfigProvider 的 `renderEmpty` 属性全局配置，所有 acud 组件（如 Table、Select、List 等）的空状态都会使用自定义的 Empty 组件：

```tsx
import {ConfigProvider} from 'acud';
import Empty from '@components/Empty';

<ConfigProvider renderEmpty={() => <Empty />}>
  <App />
</ConfigProvider>
```

### 2. 直接使用

```tsx
import Empty from '@components/Empty';

// 基础用法
<Empty />

// 自定义描述
<Empty description="暂无数据" />

// 带操作按钮
<Empty description="暂无数据，点击添加">
  <Button type="primary">添加数据</Button>
</Empty>

// 自定义图标
<Empty 
  image={<img src="custom-icon.svg" alt="empty" />}
  description="自定义空状态"
/>
```

## API

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| image | 空状态图片 | ReactNode | 默认空状态图标 |
| description | 空状态描述文字 | ReactNode | '暂无数据' |
| className | 自定义样式类名 | string | - |
| style | 自定义样式 | CSSProperties | - |
| children | 子元素（通常是操作按钮） | ReactNode | - |

## 样式定制

组件使用 CSS Modules，可以通过以下类名进行样式定制：

- `.custom-empty` - 根容器
- `.custom-empty-image` - 图片容器
- `.custom-empty-description` - 描述文字
- `.custom-empty-footer` - 底部操作区域

## 适配场景

- ✅ Table 空数据状态
- ✅ Select 无选项状态
- ✅ List 空列表状态
- ✅ 自定义空状态页面
- ✅ 搜索无结果状态

## 设计规范

- 图标尺寸：80x80px
- 描述文字：14px，颜色 #84868c
- 最小高度：200px（表格等容器中会自动调整）
- 间距：图标与文字间距 16px，文字与按钮间距 16px
