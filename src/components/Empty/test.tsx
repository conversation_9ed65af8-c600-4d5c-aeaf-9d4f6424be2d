import React from 'react';
import {Table, Button} from 'acud';
import Empty from './index';

// 测试组件，用于验证自定义 Empty 组件是否正常工作
const EmptyTest: React.FC = () => {
  const columns = [
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: '年龄',
      dataIndex: 'age',
      key: 'age'
    },
    {
      title: '地址',
      dataIndex: 'address',
      key: 'address'
    }
  ];

  return (
    <div style={{padding: '20px'}}>
      <h2>Empty 组件测试</h2>
      
      <h3>1. 基础 Empty 组件</h3>
      <div style={{border: '1px solid #d9d9d9', padding: '20px', marginBottom: '20px'}}>
        <Empty />
      </div>

      <h3>2. 带自定义描述的 Empty 组件</h3>
      <div style={{border: '1px solid #d9d9d9', padding: '20px', marginBottom: '20px'}}>
        <Empty description="这里没有任何数据" />
      </div>

      <h3>3. 带操作按钮的 Empty 组件</h3>
      <div style={{border: '1px solid #d9d9d9', padding: '20px', marginBottom: '20px'}}>
        <Empty description="暂无数据，点击按钮添加">
          <Button type="primary">添加数据</Button>
        </Empty>
      </div>

      <h3>4. Table 中的 Empty 组件（通过 ConfigProvider 全局配置）</h3>
      <div style={{border: '1px solid #d9d9d9', padding: '20px', marginBottom: '20px'}}>
        <Table 
          columns={columns} 
          dataSource={[]} 
          pagination={false}
        />
      </div>

      <h3>5. 自定义图标的 Empty 组件</h3>
      <div style={{border: '1px solid #d9d9d9', padding: '20px', marginBottom: '20px'}}>
        <Empty 
          image={<div style={{fontSize: '48px'}}>📊</div>}
          description="自定义图标的空状态"
        />
      </div>
    </div>
  );
};

export default EmptyTest;
