import React from 'react';
import classNames from 'classnames/bind';
import styles from './index.module.less';
import emptyIcon from '@assets/svg/connection-empty.svg';

const cx = classNames.bind(styles);

interface EmptyProps {
  /**
   * 空状态图片
   */
  image?: React.ReactNode;
  /**
   * 空状态描述文字
   */
  description?: React.ReactNode;
  /**
   * 自定义样式类名
   */
  className?: string;
  /**
   * 自定义样式
   */
  style?: React.CSSProperties;
  /**
   * 子元素（通常是操作按钮）
   */
  children?: React.ReactNode;
}

// 默认空状态图标
const DefaultEmptyImage = () => <img src={emptyIcon} alt="暂无数据" className={cx('default-empty-icon')} />;

const Empty: React.FC<EmptyProps> = ({image, description = '暂无数据', className, style, children}) => {
  return (
    <div className={cx('custom-empty', className)} style={style}>
      <div className={cx('custom-empty-image')}>{image || <DefaultEmptyImage />}</div>
      {children && <div className={cx('custom-empty-footer')}>{children}</div>}
      {description && <div className={cx('custom-empty-description')}>{description}</div>}
    </div>
  );
};

export default Empty;
